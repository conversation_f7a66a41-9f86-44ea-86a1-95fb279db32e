# 百度网盘 OAuth 认证完整示例

本目录包含了获取百度网盘 access_token 的完整示例代码，支持三种 OAuth 认证方式。

## 文件说明

- `complete_auth_example.py` - 完整的认证示例，包含详细的类和方法
- `simple_auth_example.py` - 简化版认证示例，提供快速使用接口
- `config_template.py` - 配置文件模板
- `auth.py` - 原始的认证示例代码

## 准备工作

### 1. 创建百度开发者应用

1. 访问 [百度开发者平台](https://console.bce.baidu.com/)
2. 创建新应用或使用现有应用
3. 获取 `client_id` 和 `client_secret`
4. 配置应用的重定向URI（授权码模式需要）

### 2. 配置应用信息

```bash
# 复制配置模板
cp config_template.py config.py

# 编辑配置文件，填入你的应用信息
```

在 `config.py` 中填入你的应用信息：

```python
# 授权码模式配置
AUTHORIZATION_CODE_CONFIG = {
    "client_id": "你的client_id",
    "client_secret": "你的client_secret",
    "redirect_uri": "oob",
    "scope": "basic,netdisk"
}

# 设备码模式配置  
DEVICE_CODE_CONFIG = {
    "client_id": "你的client_id",
    "client_secret": "你的client_secret", 
    "scope": "basic,netdisk"
}

# 默认配置
DEFAULT_CONFIG = {
    "client_id": "你的client_id",
    "client_secret": "你的client_secret"
}
```

## 使用方法

### 方式一：使用简化版示例（推荐）

```bash
python simple_auth_example.py
```

按照提示选择认证方式：
1. 授权码认证 - 适合Web应用
2. 设备码认证 - 适合设备或命令行应用
3. 刷新Token - 使用已有的refresh_token获取新的access_token

### 方式二：使用完整版示例

```bash
python complete_auth_example.py
```

### 方式三：在代码中使用

```python
from complete_auth_example import BaiduOAuthClient

# 初始化客户端
oauth_client = BaiduOAuthClient("your_client_id", "your_client_secret")

# 授权码模式
auth_url = oauth_client.get_authorization_url()
print(f"请访问: {auth_url}")
code = input("输入授权码: ")
token_data = oauth_client.get_token_by_authorization_code(code)

# 设备码模式
device_info = oauth_client.get_device_code()
print(f"请访问: {device_info['verification_url']}")
print(f"输入用户码: {device_info['user_code']}")
token_data = oauth_client.get_token_by_device_code(device_info['device_code'])

# 刷新Token
new_token_data = oauth_client.refresh_access_token(refresh_token)
```

## 三种认证方式详解

### 1. 授权码模式 (Authorization Code)

**适用场景**: Web应用、有浏览器环境的应用

**流程**:
1. 生成授权URL，用户访问并授权
2. 用户授权后获得授权码
3. 使用授权码换取access_token

**优点**: 安全性高，适合Web应用
**缺点**: 需要用户手动操作浏览器

### 2. 设备码模式 (Device Code)

**适用场景**: 设备应用、命令行工具、无浏览器环境

**流程**:
1. 获取设备码和用户码
2. 用户在其他设备上访问验证URL并输入用户码
3. 应用轮询获取access_token

**优点**: 适合无浏览器环境，用户体验好
**缺点**: 需要轮询，可能有延迟

### 3. 刷新令牌模式 (Refresh Token)

**适用场景**: 已有refresh_token，需要获取新的access_token

**流程**:
1. 使用已有的refresh_token
2. 直接获取新的access_token

**优点**: 无需用户重新授权，自动化程度高
**缺点**: 需要先通过其他方式获得refresh_token

## Token 管理

### Token 信息结构

```json
{
  "access_token": "访问令牌",
  "refresh_token": "刷新令牌", 
  "expires_in": 2592000,
  "scope": "basic netdisk",
  "session_key": "会话密钥",
  "session_secret": "会话密钥"
}
```

### Token 存储

- Token信息会自动保存到 `token.json` 文件
- 可以使用 `save_token_to_file()` 和 `load_token_from_file()` 方法管理Token

### Token 刷新

- access_token 有效期通常为30天
- 在过期前使用 refresh_token 获取新的 access_token
- refresh_token 有效期更长，通常为10年

## 错误处理

常见错误及解决方法：

1. **invalid_client**: client_id 或 client_secret 错误
2. **invalid_grant**: 授权码过期或无效
3. **authorization_pending**: 用户尚未完成授权（设备码模式）
4. **slow_down**: 请求过于频繁，需要降低轮询频率

## 安全建议

1. **保护密钥**: 不要在代码中硬编码 client_secret
2. **Token安全**: 妥善保存 access_token 和 refresh_token
3. **HTTPS**: 生产环境中使用HTTPS
4. **权限最小化**: 只申请必要的权限范围

## 示例输出

```
百度网盘 OAuth 认证工具
1. 授权码认证
2. 设备码认证
3. 刷新Token
4. 查看当前Token
请选择 (1-4): 2

=== 快速设备码认证 ===
正在获取设备码...
✅ 成功获取设备码!
设备码: a1b2c3d4e5f6g7h8i9j0
用户码: ABCD1234
验证URL: https://openapi.baidu.com/device
过期时间: 1800 秒
轮询间隔: 5 秒

请访问: https://openapi.baidu.com/device
输入用户码: ABCD1234
完成授权后按回车继续...

开始轮询获取 access_token (最多尝试 30 次)...
第 1 次尝试...
⏳ 用户尚未完成授权，继续等待...
等待 5 秒后重试...
第 2 次尝试...
✅ 成功获取 access_token!
Access Token: 121.abc123def456...
Refresh Token: 122.xyz789uvw456...
过期时间: 2592000 秒
✅ Token 信息已保存到 token.json

✅ 认证成功！Token信息已保存到 token.json
```

## 进阶使用

### 自定义配置

```python
# 自定义权限范围
oauth_client.get_device_code(scope="basic,netdisk,album")

# 自定义重定向URI
oauth_client.get_authorization_url(redirect_uri="https://your-app.com/callback")

# 自定义轮询参数
oauth_client.get_token_by_device_code(device_code, max_attempts=60)
```

### 异步使用

所有API方法都支持异步调用：

```python
# 异步调用示例
api_response = oauth_client.auth_api.oauth_token_device_code(
    client_id, scope, async_req=True
)
result = api_response.get()
```

## 故障排除

如果遇到问题，请检查：

1. 网络连接是否正常
2. client_id 和 client_secret 是否正确
3. 权限范围是否正确配置
4. 授权码是否已过期
5. 是否超过了API调用频率限制

## 相关链接

- [百度网盘开放平台文档](https://pan.baidu.com/union/doc/)
- [OAuth 2.0 规范](https://tools.ietf.org/html/rfc6749)
- [百度开发者平台](https://console.bce.baidu.com/)
