#!/usr/bin/env python3
"""
完整的百度网盘 OAuth 认证示例
包含三种获取 access_token 的方式：
1. Authorization Code 授权码模式
2. Device Code 设备码模式  
3. Refresh Token 刷新令牌模式

使用说明：
1. 首先需要在百度开发者平台创建应用获取 client_id 和 client_secret
2. 根据需要选择合适的认证方式
3. 按照示例代码的注释步骤执行
"""

import os
import sys
import time
import json
from typing import Optional, Dict, Any

# 添加项目根目录到路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)

import openapi_client
from openapi_client.api import auth_api
from openapi_client.model.oauth_token_authorization_code_response import OauthTokenAuthorizationCodeResponse
from openapi_client.model.oauth_token_refresh_token_response import OauthTokenRefreshTokenResponse
from openapi_client.model.oauth_token_device_code_response import OauthTokenDeviceCodeResponse
from openapi_client.model.oauth_token_device_token_response import OauthTokenDeviceTokenResponse


class BaiduOAuthClient:
    """百度网盘 OAuth 认证客户端"""
    
    def __init__(self, client_id: str, client_secret: str):
        """
        初始化 OAuth 客户端
        
        Args:
            client_id: 应用的 client_id
            client_secret: 应用的 client_secret
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.api_client = openapi_client.ApiClient()
        self.auth_api = auth_api.AuthApi(self.api_client)
    
    def get_authorization_url(self, redirect_uri: str = "oob", scope: str = "basic,netdisk") -> str:
        """
        获取授权码模式的授权URL
        
        Args:
            redirect_uri: 重定向URI，默认为 "oob"
            scope: 权限范围，默认为 "basic,netdisk"
            
        Returns:
            授权URL字符串
        """
        base_url = "https://openapi.baidu.com/oauth/2.0/authorize"
        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "scope": scope,
            "display": "popup"
        }
        
        param_str = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"{base_url}?{param_str}"
    
    def get_token_by_authorization_code(self, code: str, redirect_uri: str = "oob") -> Optional[Dict[str, Any]]:
        """
        通过授权码获取 access_token
        
        Args:
            code: 授权码
            redirect_uri: 重定向URI，必须与获取授权码时使用的一致
            
        Returns:
            包含 access_token 等信息的字典，失败时返回 None
        """
        try:
            print(f"正在使用授权码获取 access_token...")
            print(f"授权码: {code}")
            
            api_response = self.auth_api.oauth_token_code2token(
                code=code,
                client_id=self.client_id,
                client_secret=self.client_secret,
                redirect_uri=redirect_uri
            )
            
            result = {
                "access_token": api_response.access_token,
                "refresh_token": api_response.refresh_token,
                "expires_in": api_response.expires_in,
                "scope": api_response.scope,
                "session_key": getattr(api_response, 'session_key', None),
                "session_secret": getattr(api_response, 'session_secret', None)
            }
            
            print("✅ 成功获取 access_token!")
            print(f"Access Token: {result['access_token']}")
            print(f"Refresh Token: {result['refresh_token']}")
            print(f"过期时间: {result['expires_in']} 秒")
            
            return result
            
        except openapi_client.ApiException as e:
            print(f"❌ 获取 access_token 失败: {e}")
            return None
    
    def get_device_code(self, scope: str = "basic,netdisk") -> Optional[Dict[str, Any]]:
        """
        获取设备码和用户码
        
        Args:
            scope: 权限范围，默认为 "basic,netdisk"
            
        Returns:
            包含设备码信息的字典，失败时返回 None
        """
        try:
            print(f"正在获取设备码...")
            
            api_response = self.auth_api.oauth_token_device_code(
                client_id=self.client_id,
                scope=scope
            )
            
            result = {
                "device_code": api_response.device_code,
                "user_code": api_response.user_code,
                "verification_url": api_response.verification_url,
                "qrcode_url": getattr(api_response, 'qrcode_url', None),
                "expires_in": api_response.expires_in,
                "interval": api_response.interval
            }
            
            print("✅ 成功获取设备码!")
            print(f"设备码: {result['device_code']}")
            print(f"用户码: {result['user_code']}")
            print(f"验证URL: {result['verification_url']}")
            if result['qrcode_url']:
                print(f"二维码URL: {result['qrcode_url']}")
            print(f"过期时间: {result['expires_in']} 秒")
            print(f"轮询间隔: {result['interval']} 秒")
            
            return result
            
        except openapi_client.ApiException as e:
            print(f"❌ 获取设备码失败: {e}")
            return None
    
    def get_token_by_device_code(self, device_code: str, polling: bool = True, max_attempts: int = 30) -> Optional[Dict[str, Any]]:
        """
        通过设备码获取 access_token
        
        Args:
            device_code: 设备码
            polling: 是否自动轮询，默认为 True
            max_attempts: 最大轮询次数，默认为 30
            
        Returns:
            包含 access_token 等信息的字典，失败时返回 None
        """
        if not polling:
            # 单次请求
            return self._request_token_by_device_code(device_code)
        
        # 轮询模式
        print(f"开始轮询获取 access_token (最多尝试 {max_attempts} 次)...")
        
        for attempt in range(max_attempts):
            print(f"第 {attempt + 1} 次尝试...")
            
            result = self._request_token_by_device_code(device_code)
            if result:
                return result
            
            print(f"等待 5 秒后重试...")
            time.sleep(5)
        
        print(f"❌ 轮询超时，未能获取 access_token")
        return None
    
    def _request_token_by_device_code(self, device_code: str) -> Optional[Dict[str, Any]]:
        """内部方法：单次请求通过设备码获取 token"""
        try:
            api_response = self.auth_api.oauth_token_device_token(
                code=device_code,
                client_id=self.client_id,
                client_secret=self.client_secret
            )
            
            result = {
                "access_token": api_response.access_token,
                "refresh_token": api_response.refresh_token,
                "expires_in": api_response.expires_in,
                "scope": api_response.scope,
                "session_key": getattr(api_response, 'session_key', None),
                "session_secret": getattr(api_response, 'session_secret', None)
            }
            
            print("✅ 成功获取 access_token!")
            print(f"Access Token: {result['access_token']}")
            print(f"Refresh Token: {result['refresh_token']}")
            print(f"过期时间: {result['expires_in']} 秒")
            
            return result
            
        except openapi_client.ApiException as e:
            if "authorization_pending" in str(e):
                print("⏳ 用户尚未完成授权，继续等待...")
            elif "slow_down" in str(e):
                print("⚠️ 请求过于频繁，请稍后重试...")
            else:
                print(f"❌ 获取 access_token 失败: {e}")
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """
        使用 refresh_token 刷新 access_token
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            包含新的 access_token 等信息的字典，失败时返回 None
        """
        try:
            print(f"正在刷新 access_token...")
            print(f"Refresh Token: {refresh_token}")
            
            api_response = self.auth_api.oauth_token_refresh_token(
                refresh_token=refresh_token,
                client_id=self.client_id,
                client_secret=self.client_secret
            )
            
            result = {
                "access_token": api_response.access_token,
                "refresh_token": api_response.refresh_token,
                "expires_in": api_response.expires_in,
                "scope": api_response.scope,
                "session_key": getattr(api_response, 'session_key', None),
                "session_secret": getattr(api_response, 'session_secret', None)
            }
            
            print("✅ 成功刷新 access_token!")
            print(f"新的 Access Token: {result['access_token']}")
            print(f"新的 Refresh Token: {result['refresh_token']}")
            print(f"过期时间: {result['expires_in']} 秒")
            
            return result
            
        except openapi_client.ApiException as e:
            print(f"❌ 刷新 access_token 失败: {e}")
            return None
    
    def save_token_to_file(self, token_data: Dict[str, Any], filename: str = "token.json"):
        """
        将 token 信息保存到文件
        
        Args:
            token_data: token 数据字典
            filename: 保存的文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(token_data, f, indent=2, ensure_ascii=False)
            print(f"✅ Token 信息已保存到 {filename}")
        except Exception as e:
            print(f"❌ 保存 Token 信息失败: {e}")
    
    def load_token_from_file(self, filename: str = "token.json") -> Optional[Dict[str, Any]]:
        """
        从文件加载 token 信息
        
        Args:
            filename: 文件名
            
        Returns:
            token 数据字典，失败时返回 None
        """
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                token_data = json.load(f)
            print(f"✅ 从 {filename} 加载 Token 信息成功")
            return token_data
        except FileNotFoundError:
            print(f"❌ 文件 {filename} 不存在")
            return None
        except Exception as e:
            print(f"❌ 加载 Token 信息失败: {e}")
            return None


def demo_authorization_code_flow():
    """演示授权码模式获取 access_token"""
    print("\n" + "="*50)
    print("授权码模式演示")
    print("="*50)
    
    # 替换为你的应用信息
    client_id = "your_client_id_here"
    client_secret = "your_client_secret_here"
    
    oauth_client = BaiduOAuthClient(client_id, client_secret)
    
    # 1. 获取授权URL
    auth_url = oauth_client.get_authorization_url()
    print(f"请访问以下URL进行授权：")
    print(auth_url)
    print()
    
    # 2. 用户手动输入授权码
    code = input("请输入获取到的授权码: ").strip()
    
    # 3. 使用授权码获取 access_token
    token_data = oauth_client.get_token_by_authorization_code(code)
    
    if token_data:
        # 4. 保存 token 信息
        oauth_client.save_token_to_file(token_data, "auth_code_token.json")


def demo_device_code_flow():
    """演示设备码模式获取 access_token"""
    print("\n" + "="*50)
    print("设备码模式演示")
    print("="*50)
    
    # 替换为你的应用信息
    client_id = "your_client_id_here"
    client_secret = "your_client_secret_here"
    
    oauth_client = BaiduOAuthClient(client_id, client_secret)
    
    # 1. 获取设备码
    device_info = oauth_client.get_device_code()
    
    if device_info:
        print(f"\n请在浏览器中访问: {device_info['verification_url']}")
        print(f"并输入用户码: {device_info['user_code']}")
        print("完成授权后，程序将自动获取 access_token\n")
        
        # 2. 轮询获取 access_token
        token_data = oauth_client.get_token_by_device_code(device_info['device_code'])
        
        if token_data:
            # 3. 保存 token 信息
            oauth_client.save_token_to_file(token_data, "device_code_token.json")


def demo_refresh_token_flow():
    """演示刷新令牌模式"""
    print("\n" + "="*50)
    print("刷新令牌模式演示")
    print("="*50)
    
    # 替换为你的应用信息
    client_id = "your_client_id_here"
    client_secret = "your_client_secret_here"
    
    oauth_client = BaiduOAuthClient(client_id, client_secret)
    
    # 1. 尝试从文件加载已有的 token
    token_data = oauth_client.load_token_from_file("token.json")
    
    if token_data and "refresh_token" in token_data:
        # 2. 使用 refresh_token 刷新 access_token
        new_token_data = oauth_client.refresh_access_token(token_data["refresh_token"])
        
        if new_token_data:
            # 3. 保存新的 token 信息
            oauth_client.save_token_to_file(new_token_data, "refreshed_token.json")
    else:
        print("❌ 没有找到有效的 refresh_token，请先通过其他方式获取")


if __name__ == "__main__":
    """主函数 - 选择认证方式"""
    print("百度网盘 OAuth 认证示例")
    print("请选择认证方式：")
    print("1. 授权码模式 (Authorization Code)")
    print("2. 设备码模式 (Device Code)")  
    print("3. 刷新令牌模式 (Refresh Token)")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        demo_authorization_code_flow()
    elif choice == "2":
        demo_device_code_flow()
    elif choice == "3":
        demo_refresh_token_flow()
    else:
        print("❌ 无效的选择")
