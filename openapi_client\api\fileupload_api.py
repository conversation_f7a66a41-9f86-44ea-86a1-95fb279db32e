"""
    xpan

    xpanapi  # noqa: E501

    The version of the OpenAPI document: 0.1
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401
import sys  # noqa: F401

from openapi_client.api_client import ApiClient, Endpoint as _Endpoint
from openapi_client.model_utils import (  # noqa: F401
    check_allowed_values,
    check_validations,
    date,
    datetime,
    file_type,
    none_type,
    validate_and_convert_types
)
# from openapi_client.model.filecreateresponse import Filecreateresponse
# from openapi_client.model.fileprecreateresponse import Fileprecreateresponse


class FileuploadApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client
        self.pcssuperfile2_endpoint = _Endpoint(
            settings={
                'response_type': (dict,),
                'auth': [],
                'endpoint_path': '/rest/2.0/pcs/superfile2?method=upload&openapi=xpansdk',
                'operation_id': 'pcssuperfile2',
                'http_method': 'POST',
                'servers': [
                    {
                        'url': "https://d.pcs.baidu.com",
                        'description': "No description provided",
                    },
                ]
            },
            params_map={
                'all': [
                    'access_token',
                    'partseq',
                    'path',
                    'uploadid',
                    'type',
                    'file',
                ],
                'required': [
                    'access_token',
                    'partseq',
                    'path',
                    'uploadid',
                    'type',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'access_token':
                        (str,),
                    'partseq':
                        (str,),
                    'path':
                        (str,),
                    'uploadid':
                        (str,),
                    'type':
                        (str,),
                    'file':
                        (file_type,),
                },
                'attribute_map': {
                    'access_token': 'access_token',
                    'partseq': 'partseq',
                    'path': 'path',
                    'uploadid': 'uploadid',
                    'type': 'type',
                    'file': 'file',
                },
                'location_map': {
                    'access_token': 'query',
                    'partseq': 'query',
                    'path': 'query',
                    'uploadid': 'query',
                    'type': 'query',
                    'file': 'form',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'text/html;charset=utf8'
                ],
                'content_type': [
                    'multipart/form-data'
                ]
            },
            api_client=api_client
        )
        self.xpanfilecreate_endpoint = _Endpoint(
            settings={
                'response_type': (dict,),
                'auth': [],
                'endpoint_path': '/rest/2.0/xpan/file?method=create&openapi=xpansdk',
                'operation_id': 'xpanfilecreate',
                'http_method': 'POST',
                'servers': [
                    {
                        'url': "https://pan.baidu.com",
                        'description': "No description provided",
                    },
                ]
            },
            params_map={
                'all': [
                    'access_token',
                    'path',
                    'isdir',
                    'size',
                    'uploadid',
                    'block_list',
                    'rtype',
                ],
                'required': [
                    'access_token',
                    'path',
                    'isdir',
                    'size',
                    'uploadid',
                    'block_list',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'access_token':
                        (str,),
                    'path':
                        (str,),
                    'isdir':
                        (int,),
                    'size':
                        (int,),
                    'uploadid':
                        (str,),
                    'block_list':
                        (str,),
                    'rtype':
                        (int,),
                },
                'attribute_map': {
                    'access_token': 'access_token',
                    'path': 'path',
                    'isdir': 'isdir',
                    'size': 'size',
                    'uploadid': 'uploadid',
                    'block_list': 'block_list',
                    'rtype': 'rtype',
                },
                'location_map': {
                    'access_token': 'query',
                    'path': 'form',
                    'isdir': 'form',
                    'size': 'form',
                    'uploadid': 'form',
                    'block_list': 'form',
                    'rtype': 'form',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json; charset=UTF-8'
                ],
                'content_type': [
                    'application/x-www-form-urlencoded'
                ]
            },
            api_client=api_client
        )
        self.xpanfileprecreate_endpoint = _Endpoint(
            settings={
                'response_type': (dict,),
                'auth': [],
                'endpoint_path': '/rest/2.0/xpan/file?method=precreate&openapi=xpansdk',
                'operation_id': 'xpanfileprecreate',
                'http_method': 'POST',
                'servers': [
                    {
                        'url': "https://pan.baidu.com",
                        'description': "No description provided",
                    },
                ]
            },
            params_map={
                'all': [
                    'access_token',
                    'path',
                    'isdir',
                    'size',
                    'autoinit',
                    'block_list',
                    'rtype',
                ],
                'required': [
                    'access_token',
                    'path',
                    'isdir',
                    'size',
                    'autoinit',
                    'block_list',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'access_token':
                        (str,),
                    'path':
                        (str,),
                    'isdir':
                        (int,),
                    'size':
                        (int,),
                    'autoinit':
                        (int,),
                    'block_list':
                        (str,),
                    'rtype':
                        (int,),
                },
                'attribute_map': {
                    'access_token': 'access_token',
                    'path': 'path',
                    'isdir': 'isdir',
                    'size': 'size',
                    'autoinit': 'autoinit',
                    'block_list': 'block_list',
                    'rtype': 'rtype',
                },
                'location_map': {
                    'access_token': 'query',
                    'path': 'form',
                    'isdir': 'form',
                    'size': 'form',
                    'autoinit': 'form',
                    'block_list': 'form',
                    'rtype': 'form',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json; charset=UTF-8'
                ],
                'content_type': [
                    'application/x-www-form-urlencoded'
                ]
            },
            api_client=api_client
        )

    def pcssuperfile2(
        self,
        access_token,
        partseq,
        path,
        uploadid,
        type,
        **kwargs
    ):
        """pcssuperfile2  # noqa: E501

        分片上传，这里是实际的文件内容传送部分。一般多为大于4MB的文件，需将文件以4MB为单位切分，对切分后得到的n个分片一一调用该接口进行传送，以实现对原文件的传送（当然若不大于4MB，则直接该对文件进行传送即可）。  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.pcssuperfile2(access_token, partseq, path, uploadid, type, async_req=True)
        >>> result = thread.get()

        Args:
            access_token (str):
            partseq (str):
            path (str):
            uploadid (str):
            type (str):

        Keyword Args:
            file (file_type): 要进行传送的本地文件分片. [optional]
            _return_http_data_only (bool): response data without head status
                code and headers. Default is True.
            _preload_content (bool): if False, the urllib3.HTTPResponse object
                will be returned without reading/decoding response data.
                Default is True.
            _request_timeout (int/float/tuple): timeout setting for this request. If
                one number provided, it will be total request timeout. It can also
                be a pair (tuple) of (connection, read) timeouts.
                Default is None.
            _check_input_type (bool): specifies if type checking
                should be done one the data sent to the server.
                Default is True.
            _check_return_type (bool): specifies if type checking
                should be done one the data received from the server.
                Default is True.
            _spec_property_naming (bool): True if the variable names in the input data
                are serialized names, as specified in the OpenAPI document.
                False if the variable names in the input data
                are pythonic names, e.g. snake case (default)
            _content_type (str/None): force body content-type.
                Default is None and content-type will be predicted by allowed
                content-types and body.
            _host_index (int/None): specifies the index of the server
                that we want to use.
                Default is read from the configuration.
            async_req (bool): execute request asynchronously

        Returns:
            str
                If the method is called asynchronously, returns the request
                thread.
        """
        kwargs['async_req'] = kwargs.get(
            'async_req', False
        )
        kwargs['_return_http_data_only'] = kwargs.get(
            '_return_http_data_only', True
        )
        kwargs['_preload_content'] = kwargs.get(
            '_preload_content', True
        )
        kwargs['_request_timeout'] = kwargs.get(
            '_request_timeout', None
        )
        kwargs['_check_input_type'] = kwargs.get(
            '_check_input_type', True
        )
        kwargs['_check_return_type'] = kwargs.get(
            '_check_return_type', True
        )
        kwargs['_spec_property_naming'] = kwargs.get(
            '_spec_property_naming', False
        )
        kwargs['_content_type'] = kwargs.get(
            '_content_type')
        kwargs['_host_index'] = kwargs.get('_host_index')
        kwargs['access_token'] = \
            access_token
        kwargs['partseq'] = \
            partseq
        kwargs['path'] = \
            path
        kwargs['uploadid'] = \
            uploadid
        kwargs['type'] = \
            type
        return self.pcssuperfile2_endpoint.call_with_http_info(**kwargs)

    def xpanfilecreate(
        self,
        access_token,
        path,
        isdir,
        size,
        uploadid,
        block_list,
        **kwargs
    ):
        """xpanfilecreate  # noqa: E501

        将多个文件分片合并成一个文件，生成文件基本信息，完成文件的上传最后一步。  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.xpanfilecreate(access_token, path, isdir, size, uploadid, block_list, async_req=True)
        >>> result = thread.get()

        Args:
            access_token (str):
            path (str): 与precreate的path值保持一致
            isdir (int): isdir
            size (int): 与precreate的size值保持一致
            uploadid (str): precreate返回的uploadid
            block_list (str): 与precreate的block_list值保持一致

        Keyword Args:
            rtype (int): rtype. [optional]
            _return_http_data_only (bool): response data without head status
                code and headers. Default is True.
            _preload_content (bool): if False, the urllib3.HTTPResponse object
                will be returned without reading/decoding response data.
                Default is True.
            _request_timeout (int/float/tuple): timeout setting for this request. If
                one number provided, it will be total request timeout. It can also
                be a pair (tuple) of (connection, read) timeouts.
                Default is None.
            _check_input_type (bool): specifies if type checking
                should be done one the data sent to the server.
                Default is True.
            _check_return_type (bool): specifies if type checking
                should be done one the data received from the server.
                Default is True.
            _spec_property_naming (bool): True if the variable names in the input data
                are serialized names, as specified in the OpenAPI document.
                False if the variable names in the input data
                are pythonic names, e.g. snake case (default)
            _content_type (str/None): force body content-type.
                Default is None and content-type will be predicted by allowed
                content-types and body.
            _host_index (int/None): specifies the index of the server
                that we want to use.
                Default is read from the configuration.
            async_req (bool): execute request asynchronously

        Returns:
            Filecreateresponse
                If the method is called asynchronously, returns the request
                thread.
        """
        kwargs['async_req'] = kwargs.get(
            'async_req', False
        )
        kwargs['_return_http_data_only'] = kwargs.get(
            '_return_http_data_only', True
        )
        kwargs['_preload_content'] = kwargs.get(
            '_preload_content', True
        )
        kwargs['_request_timeout'] = kwargs.get(
            '_request_timeout', None
        )
        kwargs['_check_input_type'] = kwargs.get(
            '_check_input_type', True
        )
        kwargs['_check_return_type'] = kwargs.get(
            '_check_return_type', True
        )
        kwargs['_spec_property_naming'] = kwargs.get(
            '_spec_property_naming', False
        )
        kwargs['_content_type'] = kwargs.get(
            '_content_type')
        kwargs['_host_index'] = kwargs.get('_host_index')
        kwargs['access_token'] = \
            access_token
        kwargs['path'] = \
            path
        kwargs['isdir'] = \
            isdir
        kwargs['size'] = \
            size
        kwargs['uploadid'] = \
            uploadid
        kwargs['block_list'] = \
            block_list
        return self.xpanfilecreate_endpoint.call_with_http_info(**kwargs)

    def xpanfileprecreate(
        self,
        access_token,
        path,
        isdir,
        size,
        autoinit,
        block_list,
        **kwargs
    ):
        """xpanfileprecreate  # noqa: E501

        文件预上传，用于获取上传任务id，既uploadid  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.xpanfileprecreate(access_token, path, isdir, size, autoinit, block_list, async_req=True)
        >>> result = thread.get()

        Args:
            access_token (str):
            path (str): 对于一般的第三方软件应用，路径以 \\\"/apps/your-app-name/\\\" 开头。对于小度等硬件应用，路径一般 \\\"/来自：小度设备/\\\" 开头。对于定制化配置的硬件应用，根据配置情况进行填写。
            isdir (int): isdir
            size (int): size
            autoinit (int): autoinit
            block_list (str): 由MD5字符串组成的list

        Keyword Args:
            rtype (int): rtype. [optional]
            _return_http_data_only (bool): response data without head status
                code and headers. Default is True.
            _preload_content (bool): if False, the urllib3.HTTPResponse object
                will be returned without reading/decoding response data.
                Default is True.
            _request_timeout (int/float/tuple): timeout setting for this request. If
                one number provided, it will be total request timeout. It can also
                be a pair (tuple) of (connection, read) timeouts.
                Default is None.
            _check_input_type (bool): specifies if type checking
                should be done one the data sent to the server.
                Default is True.
            _check_return_type (bool): specifies if type checking
                should be done one the data received from the server.
                Default is True.
            _spec_property_naming (bool): True if the variable names in the input data
                are serialized names, as specified in the OpenAPI document.
                False if the variable names in the input data
                are pythonic names, e.g. snake case (default)
            _content_type (str/None): force body content-type.
                Default is None and content-type will be predicted by allowed
                content-types and body.
            _host_index (int/None): specifies the index of the server
                that we want to use.
                Default is read from the configuration.
            async_req (bool): execute request asynchronously

        Returns:
            Fileprecreateresponse
                If the method is called asynchronously, returns the request
                thread.
        """
        kwargs['async_req'] = kwargs.get(
            'async_req', False
        )
        kwargs['_return_http_data_only'] = kwargs.get(
            '_return_http_data_only', True
        )
        kwargs['_preload_content'] = kwargs.get(
            '_preload_content', True
        )
        kwargs['_request_timeout'] = kwargs.get(
            '_request_timeout', None
        )
        kwargs['_check_input_type'] = kwargs.get(
            '_check_input_type', True
        )
        kwargs['_check_return_type'] = kwargs.get(
            '_check_return_type', True
        )
        kwargs['_spec_property_naming'] = kwargs.get(
            '_spec_property_naming', False
        )
        kwargs['_content_type'] = kwargs.get(
            '_content_type')
        kwargs['_host_index'] = kwargs.get('_host_index')
        kwargs['access_token'] = \
            access_token
        kwargs['path'] = \
            path
        kwargs['isdir'] = \
            isdir
        kwargs['size'] = \
            size
        kwargs['autoinit'] = \
            autoinit
        kwargs['block_list'] = \
            block_list
        return self.xpanfileprecreate_endpoint.call_with_http_info(**kwargs)

