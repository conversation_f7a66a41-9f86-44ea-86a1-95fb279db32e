"""
    xpan

    xpanapi  # noqa: E501

    The version of the OpenAPI document: 0.1
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401
import sys  # noqa: F401

from openapi_client.api_client import ApiClient, Endpoint as _Endpoint
from openapi_client.model_utils import (  # noqa: F401
    check_allowed_values,
    check_validations,
    date,
    datetime,
    file_type,
    none_type,
    validate_and_convert_types
)


class FileinfoApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client
        self.xpanfiledoclist_endpoint = _Endpoint(
            settings={
                'response_type': (dict,),
                'auth': [],
                'endpoint_path': '/rest/2.0/xpan/file?method=doclist&openapi=xpansdk',
                'operation_id': 'xpanfiledoclist',
                'http_method': 'GET',
                'servers': [
                    {
                        'url': "https://pan.baidu.com",
                        'description': "No description provided",
                    },
                ]
            },
            params_map={
                'all': [
                    'access_token',
                    'parent_path',
                    'recursion',
                    'page',
                    'num',
                    'order',
                    'desc',
                    'web',
                ],
                'required': [
                    'access_token',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'access_token':
                        (str,),
                    'parent_path':
                        (str,),
                    'recursion':
                        (str,),
                    'page':
                        (int,),
                    'num':
                        (int,),
                    'order':
                        (str,),
                    'desc':
                        (str,),
                    'web':
                        (str,),
                },
                'attribute_map': {
                    'access_token': 'access_token',
                    'parent_path': 'parent_path',
                    'recursion': 'recursion',
                    'page': 'page',
                    'num': 'num',
                    'order': 'order',
                    'desc': 'desc',
                    'web': 'web',
                },
                'location_map': {
                    'access_token': 'query',
                    'parent_path': 'query',
                    'recursion': 'query',
                    'page': 'query',
                    'num': 'query',
                    'order': 'query',
                    'desc': 'query',
                    'web': 'query',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json; charset=UTF-8'
                ],
                'content_type': [],
            },
            api_client=api_client
        )
        self.xpanfileimagelist_endpoint = _Endpoint(
            settings={
                'response_type': (dict,),
                'auth': [],
                'endpoint_path': '/rest/2.0/xpan/file?method=imagelist&openapi=xpansdk',
                'operation_id': 'xpanfileimagelist',
                'http_method': 'GET',
                'servers': [
                    {
                        'url': "https://pan.baidu.com",
                        'description': "No description provided",
                    },
                ]
            },
            params_map={
                'all': [
                    'access_token',
                    'parent_path',
                    'recursion',
                    'page',
                    'num',
                    'order',
                    'desc',
                    'web',
                ],
                'required': [
                    'access_token',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'access_token':
                        (str,),
                    'parent_path':
                        (str,),
                    'recursion':
                        (str,),
                    'page':
                        (int,),
                    'num':
                        (int,),
                    'order':
                        (str,),
                    'desc':
                        (str,),
                    'web':
                        (str,),
                },
                'attribute_map': {
                    'access_token': 'access_token',
                    'parent_path': 'parent_path',
                    'recursion': 'recursion',
                    'page': 'page',
                    'num': 'num',
                    'order': 'order',
                    'desc': 'desc',
                    'web': 'web',
                },
                'location_map': {
                    'access_token': 'query',
                    'parent_path': 'query',
                    'recursion': 'query',
                    'page': 'query',
                    'num': 'query',
                    'order': 'query',
                    'desc': 'query',
                    'web': 'query',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json; charset=UTF-8'
                ],
                'content_type': [],
            },
            api_client=api_client
        )
        self.xpanfilelist_endpoint = _Endpoint(
            settings={
                'response_type': (dict,),
                'auth': [],
                'endpoint_path': '/rest/2.0/xpan/file?method=list&openapi=xpansdk',
                'operation_id': 'xpanfilelist',
                'http_method': 'GET',
                'servers': [
                    {
                        'url': "https://pan.baidu.com",
                        'description': "No description provided",
                    },
                ]
            },
            params_map={
                'all': [
                    'access_token',
                    'dir',
                    'folder',
                    'start',
                    'limit',
                    'order',
                    'desc',
                    'web',
                    'showempty',
                ],
                'required': [
                    'access_token',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'access_token':
                        (str,),
                    'dir':
                        (str,),
                    'folder':
                        (str,),
                    'start':
                        (str,),
                    'limit':
                        (int,),
                    'order':
                        (str,),
                    'desc':
                        (int,),
                    'web':
                        (str,),
                    'showempty':
                        (int,),
                },
                'attribute_map': {
                    'access_token': 'access_token',
                    'dir': 'dir',
                    'folder': 'folder',
                    'start': 'start',
                    'limit': 'limit',
                    'order': 'order',
                    'desc': 'desc',
                    'web': 'web',
                    'showempty': 'showempty',
                },
                'location_map': {
                    'access_token': 'query',
                    'dir': 'query',
                    'folder': 'query',
                    'start': 'query',
                    'limit': 'query',
                    'order': 'query',
                    'desc': 'query',
                    'web': 'query',
                    'showempty': 'query',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json; charset=UTF-8'
                ],
                'content_type': [],
            },
            api_client=api_client
        )
        self.xpanfilesearch_endpoint = _Endpoint(
            settings={
                'response_type': (dict,),
                'auth': [],
                'endpoint_path': '/rest/2.0/xpan/file?method=search&openapi=xpansdk',
                'operation_id': 'xpanfilesearch',
                'http_method': 'GET',
                'servers': [
                    {
                        'url': "https://pan.baidu.com",
                        'description': "No description provided",
                    },
                ]
            },
            params_map={
                'all': [
                    'access_token',
                    'key',
                    'web',
                    'num',
                    'page',
                    'dir',
                    'recursion',
                ],
                'required': [
                    'access_token',
                    'key',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'access_token':
                        (str,),
                    'key':
                        (str,),
                    'web':
                        (str,),
                    'num':
                        (str,),
                    'page':
                        (str,),
                    'dir':
                        (str,),
                    'recursion':
                        (str,),
                },
                'attribute_map': {
                    'access_token': 'access_token',
                    'key': 'key',
                    'web': 'web',
                    'num': 'num',
                    'page': 'page',
                    'dir': 'dir',
                    'recursion': 'recursion',
                },
                'location_map': {
                    'access_token': 'query',
                    'key': 'query',
                    'web': 'query',
                    'num': 'query',
                    'page': 'query',
                    'dir': 'query',
                    'recursion': 'query',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json; charset=UTF-8'
                ],
                'content_type': [],
            },
            api_client=api_client
        )

    def xpanfiledoclist(
        self,
        access_token,
        **kwargs
    ):
        """xpanfiledoclist  # noqa: E501

        file doclist  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.xpanfiledoclist(access_token, async_req=True)
        >>> result = thread.get()

        Args:
            access_token (str):

        Keyword Args:
            parent_path (str): [optional]
            recursion (str): [optional]
            page (int): [optional]
            num (int): [optional]
            order (str): [optional]
            desc (str): [optional]
            web (str): [optional]
            _return_http_data_only (bool): response data without head status
                code and headers. Default is True.
            _preload_content (bool): if False, the urllib3.HTTPResponse object
                will be returned without reading/decoding response data.
                Default is True.
            _request_timeout (int/float/tuple): timeout setting for this request. If
                one number provided, it will be total request timeout. It can also
                be a pair (tuple) of (connection, read) timeouts.
                Default is None.
            _check_input_type (bool): specifies if type checking
                should be done one the data sent to the server.
                Default is True.
            _check_return_type (bool): specifies if type checking
                should be done one the data received from the server.
                Default is True.
            _spec_property_naming (bool): True if the variable names in the input data
                are serialized names, as specified in the OpenAPI document.
                False if the variable names in the input data
                are pythonic names, e.g. snake case (default)
            _content_type (str/None): force body content-type.
                Default is None and content-type will be predicted by allowed
                content-types and body.
            _host_index (int/None): specifies the index of the server
                that we want to use.
                Default is read from the configuration.
            async_req (bool): execute request asynchronously

        Returns:
            str
                If the method is called asynchronously, returns the request
                thread.
        """
        kwargs['async_req'] = kwargs.get(
            'async_req', False
        )
        kwargs['_return_http_data_only'] = kwargs.get(
            '_return_http_data_only', True
        )
        kwargs['_preload_content'] = kwargs.get(
            '_preload_content', True
        )
        kwargs['_request_timeout'] = kwargs.get(
            '_request_timeout', None
        )
        kwargs['_check_input_type'] = kwargs.get(
            '_check_input_type', True
        )
        kwargs['_check_return_type'] = kwargs.get(
            '_check_return_type', True
        )
        kwargs['_spec_property_naming'] = kwargs.get(
            '_spec_property_naming', False
        )
        kwargs['_content_type'] = kwargs.get(
            '_content_type')
        kwargs['_host_index'] = kwargs.get('_host_index')
        kwargs['access_token'] = \
            access_token
        return self.xpanfiledoclist_endpoint.call_with_http_info(**kwargs)

    def xpanfileimagelist(
        self,
        access_token,
        **kwargs
    ):
        """xpanfileimagelist  # noqa: E501

        file imagelist  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.xpanfileimagelist(access_token, async_req=True)
        >>> result = thread.get()

        Args:
            access_token (str):

        Keyword Args:
            parent_path (str): [optional]
            recursion (str): [optional]
            page (int): [optional]
            num (int): [optional]
            order (str): [optional]
            desc (str): [optional]
            web (str): [optional]
            _return_http_data_only (bool): response data without head status
                code and headers. Default is True.
            _preload_content (bool): if False, the urllib3.HTTPResponse object
                will be returned without reading/decoding response data.
                Default is True.
            _request_timeout (int/float/tuple): timeout setting for this request. If
                one number provided, it will be total request timeout. It can also
                be a pair (tuple) of (connection, read) timeouts.
                Default is None.
            _check_input_type (bool): specifies if type checking
                should be done one the data sent to the server.
                Default is True.
            _check_return_type (bool): specifies if type checking
                should be done one the data received from the server.
                Default is True.
            _spec_property_naming (bool): True if the variable names in the input data
                are serialized names, as specified in the OpenAPI document.
                False if the variable names in the input data
                are pythonic names, e.g. snake case (default)
            _content_type (str/None): force body content-type.
                Default is None and content-type will be predicted by allowed
                content-types and body.
            _host_index (int/None): specifies the index of the server
                that we want to use.
                Default is read from the configuration.
            async_req (bool): execute request asynchronously

        Returns:
            str
                If the method is called asynchronously, returns the request
                thread.
        """
        kwargs['async_req'] = kwargs.get(
            'async_req', False
        )
        kwargs['_return_http_data_only'] = kwargs.get(
            '_return_http_data_only', True
        )
        kwargs['_preload_content'] = kwargs.get(
            '_preload_content', True
        )
        kwargs['_request_timeout'] = kwargs.get(
            '_request_timeout', None
        )
        kwargs['_check_input_type'] = kwargs.get(
            '_check_input_type', True
        )
        kwargs['_check_return_type'] = kwargs.get(
            '_check_return_type', True
        )
        kwargs['_spec_property_naming'] = kwargs.get(
            '_spec_property_naming', False
        )
        kwargs['_content_type'] = kwargs.get(
            '_content_type')
        kwargs['_host_index'] = kwargs.get('_host_index')
        kwargs['access_token'] = \
            access_token
        return self.xpanfileimagelist_endpoint.call_with_http_info(**kwargs)

    def xpanfilelist(
        self,
        access_token,
        **kwargs
    ):
        """xpanfilelist  # noqa: E501

        file list  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.xpanfilelist(access_token, async_req=True)
        >>> result = thread.get()

        Args:
            access_token (str):

        Keyword Args:
            dir (str): [optional]
            folder (str): [optional]
            start (str): [optional]
            limit (int): [optional]
            order (str): [optional]
            desc (int): [optional]
            web (str): [optional]
            showempty (int): [optional]
            _return_http_data_only (bool): response data without head status
                code and headers. Default is True.
            _preload_content (bool): if False, the urllib3.HTTPResponse object
                will be returned without reading/decoding response data.
                Default is True.
            _request_timeout (int/float/tuple): timeout setting for this request. If
                one number provided, it will be total request timeout. It can also
                be a pair (tuple) of (connection, read) timeouts.
                Default is None.
            _check_input_type (bool): specifies if type checking
                should be done one the data sent to the server.
                Default is True.
            _check_return_type (bool): specifies if type checking
                should be done one the data received from the server.
                Default is True.
            _spec_property_naming (bool): True if the variable names in the input data
                are serialized names, as specified in the OpenAPI document.
                False if the variable names in the input data
                are pythonic names, e.g. snake case (default)
            _content_type (str/None): force body content-type.
                Default is None and content-type will be predicted by allowed
                content-types and body.
            _host_index (int/None): specifies the index of the server
                that we want to use.
                Default is read from the configuration.
            async_req (bool): execute request asynchronously

        Returns:
            str
                If the method is called asynchronously, returns the request
                thread.
        """
        kwargs['async_req'] = kwargs.get(
            'async_req', False
        )
        kwargs['_return_http_data_only'] = kwargs.get(
            '_return_http_data_only', True
        )
        kwargs['_preload_content'] = kwargs.get(
            '_preload_content', True
        )
        kwargs['_request_timeout'] = kwargs.get(
            '_request_timeout', None
        )
        kwargs['_check_input_type'] = kwargs.get(
            '_check_input_type', True
        )
        kwargs['_check_return_type'] = kwargs.get(
            '_check_return_type', True
        )
        kwargs['_spec_property_naming'] = kwargs.get(
            '_spec_property_naming', False
        )
        kwargs['_content_type'] = kwargs.get(
            '_content_type')
        kwargs['_host_index'] = kwargs.get('_host_index')
        kwargs['access_token'] = \
            access_token
        return self.xpanfilelist_endpoint.call_with_http_info(**kwargs)

    def xpanfilesearch(
        self,
        access_token,
        key,
        **kwargs
    ):
        """xpanfilesearch  # noqa: E501

        file search  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.xpanfilesearch(access_token, key, async_req=True)
        >>> result = thread.get()

        Args:
            access_token (str):
            key (str):

        Keyword Args:
            web (str): [optional]
            num (str): [optional]
            page (str): [optional]
            dir (str): [optional]
            recursion (str): [optional]
            _return_http_data_only (bool): response data without head status
                code and headers. Default is True.
            _preload_content (bool): if False, the urllib3.HTTPResponse object
                will be returned without reading/decoding response data.
                Default is True.
            _request_timeout (int/float/tuple): timeout setting for this request. If
                one number provided, it will be total request timeout. It can also
                be a pair (tuple) of (connection, read) timeouts.
                Default is None.
            _check_input_type (bool): specifies if type checking
                should be done one the data sent to the server.
                Default is True.
            _check_return_type (bool): specifies if type checking
                should be done one the data received from the server.
                Default is True.
            _spec_property_naming (bool): True if the variable names in the input data
                are serialized names, as specified in the OpenAPI document.
                False if the variable names in the input data
                are pythonic names, e.g. snake case (default)
            _content_type (str/None): force body content-type.
                Default is None and content-type will be predicted by allowed
                content-types and body.
            _host_index (int/None): specifies the index of the server
                that we want to use.
                Default is read from the configuration.
            async_req (bool): execute request asynchronously

        Returns:
            str
                If the method is called asynchronously, returns the request
                thread.
        """
        kwargs['async_req'] = kwargs.get(
            'async_req', False
        )
        kwargs['_return_http_data_only'] = kwargs.get(
            '_return_http_data_only', True
        )
        kwargs['_preload_content'] = kwargs.get(
            '_preload_content', True
        )
        kwargs['_request_timeout'] = kwargs.get(
            '_request_timeout', None
        )
        kwargs['_check_input_type'] = kwargs.get(
            '_check_input_type', True
        )
        kwargs['_check_return_type'] = kwargs.get(
            '_check_return_type', True
        )
        kwargs['_spec_property_naming'] = kwargs.get(
            '_spec_property_naming', False
        )
        kwargs['_content_type'] = kwargs.get(
            '_content_type')
        kwargs['_host_index'] = kwargs.get('_host_index')
        kwargs['access_token'] = \
            access_token
        kwargs['key'] = \
            key
        return self.xpanfilesearch_endpoint.call_with_http_info(**kwargs)

