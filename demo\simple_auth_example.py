#!/usr/bin/env python3
"""
简化版的百度网盘 OAuth 认证示例
使用配置文件管理应用信息，提供更简洁的使用方式
"""

import os
import sys

# 添加项目根目录到路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)

from complete_auth_example import BaiduOAuthClient

# 尝试导入配置文件
try:
    from config import DEFAULT_CONFIG, AUTHORIZATION_CODE_CONFIG, DEVICE_CODE_CONFIG
    print("✅ 成功加载配置文件")
except ImportError:
    print("❌ 未找到配置文件，请先复制 config_template.py 为 config.py 并填入你的应用信息")
    sys.exit(1)


def quick_auth_by_code():
    """快速授权码认证"""
    print("\n=== 快速授权码认证 ===")
    
    config = AUTHORIZATION_CODE_CONFIG
    oauth_client = BaiduOAuthClient(config["client_id"], config["client_secret"])
    
    # 显示授权URL
    auth_url = oauth_client.get_authorization_url(
        redirect_uri=config["redirect_uri"],
        scope=config["scope"]
    )
    print(f"授权URL: {auth_url}")
    
    # 获取授权码
    code = input("请输入授权码: ").strip()
    
    # 获取token
    token_data = oauth_client.get_token_by_authorization_code(
        code, config["redirect_uri"]
    )
    
    if token_data:
        oauth_client.save_token_to_file(token_data)
        return token_data
    return None


def quick_auth_by_device():
    """快速设备码认证"""
    print("\n=== 快速设备码认证 ===")
    
    config = DEVICE_CODE_CONFIG
    oauth_client = BaiduOAuthClient(config["client_id"], config["client_secret"])
    
    # 获取设备码
    device_info = oauth_client.get_device_code(config["scope"])
    if not device_info:
        return None
    
    print(f"\n请访问: {device_info['verification_url']}")
    print(f"输入用户码: {device_info['user_code']}")
    input("完成授权后按回车继续...")
    
    # 获取token
    token_data = oauth_client.get_token_by_device_code(
        device_info['device_code'], polling=True
    )
    
    if token_data:
        oauth_client.save_token_to_file(token_data)
        return token_data
    return None


def quick_refresh_token():
    """快速刷新token"""
    print("\n=== 快速刷新Token ===")
    
    config = DEFAULT_CONFIG
    oauth_client = BaiduOAuthClient(config["client_id"], config["client_secret"])
    
    # 从文件加载token
    token_data = oauth_client.load_token_from_file()
    if not token_data or "refresh_token" not in token_data:
        print("❌ 没有找到有效的refresh_token")
        return None
    
    # 刷新token
    new_token_data = oauth_client.refresh_access_token(token_data["refresh_token"])
    
    if new_token_data:
        oauth_client.save_token_to_file(new_token_data)
        return new_token_data
    return None


def main():
    """主函数"""
    print("百度网盘 OAuth 认证工具")
    print("1. 授权码认证")
    print("2. 设备码认证") 
    print("3. 刷新Token")
    print("4. 查看当前Token")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        token_data = quick_auth_by_code()
    elif choice == "2":
        token_data = quick_auth_by_device()
    elif choice == "3":
        token_data = quick_refresh_token()
    elif choice == "4":
        config = DEFAULT_CONFIG
        oauth_client = BaiduOAuthClient(config["client_id"], config["client_secret"])
        token_data = oauth_client.load_token_from_file()
        if token_data:
            print(f"Access Token: {token_data.get('access_token', 'N/A')}")
            print(f"Refresh Token: {token_data.get('refresh_token', 'N/A')}")
            print(f"过期时间: {token_data.get('expires_in', 'N/A')} 秒")
        return
    else:
        print("❌ 无效选择")
        return
    
    if token_data:
        print("\n✅ 认证成功！Token信息已保存到 token.json")
    else:
        print("\n❌ 认证失败")


if __name__ == "__main__":
    main()
