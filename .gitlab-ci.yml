# ref: https://docs.gitlab.com/ee/ci/README.html

stages:
  - test

.tests:
  stage: test
  script:
   - pip install -r requirements.txt
   - pip install -r test-requirements.txt
   - pytest --cov=openapi_client

test-3.6:
  extends: .tests
  image: python:3.6-alpine
test-3.7:
  extends: .tests
  image: python:3.7-alpine
test-3.8:
  extends: .tests
  image: python:3.8-alpine
test-3.9:
  extends: .tests
  image: python:3.9-alpine
