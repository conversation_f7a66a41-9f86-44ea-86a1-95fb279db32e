#!/usr/bin/env python3
"""
配置文件模板
请复制此文件为 config.py 并填入你的应用信息
"""

# 百度开发者平台应用信息
# 请在 https://console.bce.baidu.com/ 创建应用后获取以下信息

# 授权码模式应用配置
AUTHORIZATION_CODE_CONFIG = {
    "client_id": "4uFDsPhyf5K5tokkiRrGDTyuh3ctlw5P",
    "client_secret": "EBgPpCmxo4SIfV9vtD45YHdTtnSPkIRo",
    "redirect_uri": "oob",  # 通常使用 "oob" 表示不重定向
    "scope": "basic,netdisk"  # 权限范围
}

# 设备码模式应用配置
DEVICE_CODE_CONFIG = {
    "client_id": "4uFDsPhyf5K5tokkiRrGDTyuh3ctlw5P",
    "client_secret": "EBgPpCmxo4SIfV9vtD45YHdTtnSPkIRo",
    "scope": "basic,netdisk"  # 权限范围
}

# 刷新令牌配置（使用任一模式获取的 refresh_token）
REFRESH_TOKEN_CONFIG = {
    "client_id": "4uFDsPhyf5K5tokkiRrGDTyuh3ctlw5P",
    "client_secret": "EBgPpCmxo4SIfV9vtD45YHdTtnSPkIRo",
}

# 默认配置（如果只有一个应用，可以使用这个）
DEFAULT_CONFIG = {
    "client_id": "4uFDsPhyf5K5tokkiRrGDTyuh3ctlw5P",
    "client_secret": "EBgPpCmxo4SIfV9vtD45YHdTtnSPkIRo",
}
